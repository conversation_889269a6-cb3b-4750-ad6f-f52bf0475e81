import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Input from "./input";
import Button from "../button/button";

const meta: Meta<typeof Input> = {
  title: "Components/Input",
  component: Input,
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    name: { control: "text" },
    value: { control: "text" },
    error: { control: "text" },
    mask: {
      control: { type: "select" },
      options: ["inr", "date", "mobile_number", "pan", "number"],
    },
    textTransform: {
      control: { type: "select" },
      options: ["uppercase", "lowercase", "capitalize", "none"],
    },
    readOnly: { control: "boolean" },
    type: {
      control: { type: "select" },
      options: ["text", "password", "email", "number", "url", "search", "date"],
    },
    density: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    onInput: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    label: "Name",
    name: "username",
    type: "text",
    density: "compact",
  },
  render: (args) => {
    return (
      <form
        onSubmit={(e) => {
          e.preventDefault();
          const data = new FormData(e.target as HTMLFormElement);
          alert("Form submitted with value: " + data.get("username"));
        }}
      >
        <Input {...args} />
        <Button type="submit" className="mt-4">
          Submit
        </Button>
      </form>
    );
  },
};

export const WithPrefix: Story = {
  args: {
    name: "phone",
    label: "Phone Number",
  },
  render: (args) => {
    return <Input {...args} prefix={<span>+91</span>} />;
  },
};

export const WithError: Story = {
  args: {
    label: "Email",
    name: "email",
    type: "email",
    error: "Please enter a valid email address",
  },
  render: (args) => {
    return <Input {...args} />;
  },
};

export const WithHelp: Story = {
  args: {
    label: "PAN Number",
    name: "pan",
    mask: "pan",
  },
  render: (args) => {
    return (
      <Input
        {...args}
        help={
          <span className="text-body2 text-black-60">
            Enter your 10-character PAN number (e.g., **********)
          </span>
        }
      />
    );
  },
};

export const NumberMask: Story = {
  args: {
    label: "Amount",
    name: "amount",
    mask: "number",
  },
  render: (args) => {
    return (
      <Input
        {...args}
        help={
          <span className="text-body2 text-black-60">
            Try entering "123456" - it should not be truncated to "123"
          </span>
        }
      />
    );
  },
};

export const INRMask: Story = {
  args: {
    label: "Annual Income",
    name: "income",
    mask: "inr",
  },
  render: (args) => {
    return (
      <Input
        {...args}
        help={
          <span className="text-body2 text-black-60">
            Try entering large numbers like "1000000" - they should be preserved
          </span>
        }
      />
    );
  },
};
