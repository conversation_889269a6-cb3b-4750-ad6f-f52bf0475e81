import { useState, useMemo, useId, useCallback } from "react";
import clsx from "clsx";
import type { InputProps } from "./types";
import IMask from "imask";
import { IMaskInput } from "react-imask";

export default function Input({
  value,
  id,
  error,
  mask,
  label,
  textTransform,
  density = "default",
  prefix,
  suffix,
  help,
  readOnly,
  className,
  onBlur,
  ...rest
}: InputProps) {
  const generatedId = useId();
  const inputId = id || `input-${generatedId}`;
  const [focused, setFocused] = useState(false);

  const pristine = useMemo(() => !value && !focused, [value, focused]);

  const maskOptions = useMemo(() => {
    switch (mask) {
      case "date":
        return IMask.createMask({
          mask: "00/00/0000",
          definitions: {
            0: /[0-9]/,
          },
          lazy: true,
          autofix: true,
        });
      case "inr":
        return IMask.createMask({
          mask: "₹num",
          blocks: {
            num: {
              mask: Number,
              thousandsSeparator: ",",
              min: 0,
              max: Number.MAX_SAFE_INTEGER,
              scale: 0, // No decimal places
              signed: false, // Only positive numbers
              normalizeZeros: false, // Preserve leading zeros
              padFractionalZeros: false,
            },
          },
        });
      case "number":
        return IMask.createMask({
          mask: "num",
          blocks: {
            num: {
              mask: Number,
              thousandsSeparator: ",",
              min: 0,
              max: Number.MAX_SAFE_INTEGER,
              scale: 0, // No decimal places
              signed: false, // Only positive numbers
              normalizeZeros: false, // Preserve leading zeros
              padFractionalZeros: false,
            },
          },
        });
      case "mobile_number":
        return IMask.createMask({
          mask: "0000000000",
        });
      case "otp":
        return IMask.createMask({
          mask: "000000",
        });
      case "pan":
        return IMask.createMask({
          mask: "**********",
          definitions: {
            A: /[A-Z]/,
            0: /[0-9]/,
          },
          prepare: (str: string) => str.toUpperCase(),
          placeholderChar: " ",
          lazy: true,
        });
      default:
        return undefined;
    }
  }, [mask]);

  const handleFocus = useCallback(() => {
    if (!readOnly) setFocused(true);
  }, [readOnly]);

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      onBlur?.(e);
    },
    [onBlur]
  );

  const inputProps = useMemo(() => {
    return {
      id: inputId,
      className: clsx(
        className,
        "text-heading3 pt-4 w-full outline-none",
        textTransform,
        density,
        {
          "text-transparent": pristine,
          "cursor-not-allowed": readOnly,
        }
      ),
      ...rest,
      value,
      onFocus: handleFocus,
      onBlur: handleBlur,
      readOnly,
    };
  }, [
    inputId,
    className,
    textTransform,
    density,
    pristine,
    readOnly,
    rest,
    value,
    handleFocus,
    handleBlur,
  ]);

  return (
    <div>
      <div
        className={clsx("bg-black-5 rounded-xl border", {
          "border-red": error,
        })}
      >
        <div
          className={clsx(
            "relative flex cursor-pointer items-center gap-3 rounded-xl bg-white px-3",
            {
              "border-red": error,
            }
          )}
        >
          {prefix}
          <div className="flex-grow py-2">
            <label
              className={clsx(
                "text-black-40 absolute cursor-pointer transition-all duration-300 ease-in-out",
                pristine
                  ? "text-heading3 top-1/2 translate-y-[-50%]"
                  : "text-body2 top-2",
                density === "compact" ? "text-heading4" : ""
              )}
              htmlFor={inputId}
            >
              {label}
            </label>
            {maskOptions ? (
              // @ts-expect-error IMaskInput is not typed correctly
              <IMaskInput {...inputProps} mask={maskOptions} />
            ) : (
              <input {...inputProps} />
            )}
          </div>
          {suffix}
        </div>
        {help && <div className="px-4 py-2 empty:hidden">{help}</div>}
      </div>
      {error && <div className="text-red mt-2">{error}</div>}
    </div>
  );
}
