import Input from "@/components/ui/input/input";
import { Field as FormikField, type FormikProps } from "formik";
import { type FormValues } from "./schema";
import { useQuery } from "@tanstack/react-query";
import { getAnnualIncome } from "@/queries/form15-flow";

export default function FormFields({
  errors,
  submitCount,
}: FormikProps<FormValues>) {
  const annualIncomeQuery = useQuery({
    queryKey: ["annual-income"],
    queryFn: () => getAnnualIncome(),
    retry(failureCount) {
      return failureCount < 2;
    },
    retryDelay: 500,
  });

  return (
    <>
      <FormikField
        name="annualIncome"
        label="Annual income"
        as={Input}
        values={annualIncomeQuery.data?.annualIncome ?? ""}
        error={submitCount > 0 ? errors.annualIncome : undefined}
        mask="inr"
      />
    </>
  );
}
