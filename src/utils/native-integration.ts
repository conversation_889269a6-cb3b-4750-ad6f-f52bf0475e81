import type { J<PERSON>NValue } from "hotwire-native-bolt/dist/types";
import { isHotwireNative } from "./routing";

// Type definitions for Strada components
type StradaComponent =
  | "analytics"
  | "authentication"
  | "cashfree"
  | "digio-esign"
  | "digio-kyc"
  | "hyperkyc"
  | "share"
  | "email";

/**
 * Check if Flutter WebView is available
 */
function isFlutterWebView(): boolean {
  return typeof window.flutter_inappwebview?.callHandler === "function";
}

/**
 * Check if a Strada component is supported
 */
function supportsStradaComponent(component: StradaComponent): boolean {
  return window.Strada.web.supportsComponent(component) ?? false;
}

/**
 * Analytics helper functions
 */
export const analytics = {
  /**
   * Check if analytics component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("analytics") || isFlutterWebView();
  },

  /**
   * Track an event via native analytics
   */
  track(event: string, properties: Record<string, JSONValue> = {}) {
    if (supportsStradaComponent("analytics")) {
      return window.Strada!.web.send({
        component: "analytics",
        event: "track",
        data: {
          event,
          properties,
          metadata: { url: window.location.href },
        },
      });
      return;
    }

    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler(
        "trackAnalyticsEvent",
        event,
        properties
      );
    }
  },
};

/**
 * Authentication helper functions
 */
export const authentication = {
  /**
   * Check if authentication component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("authentication") || isFlutterWebView();
  },

  /**
   * Get native token
   */
  getToken(type: "access_token" | "refresh_token"): Promise<string | null> {
    if (isHotwireNative()) {
      return new Promise((resolve) => {
        window.Strada.web.send({
          component: "authentication",
          event: "getToken",
          data: { type, metadata: { url: window.location.href } },
          callback: ({ data }) => {
            resolve(data?.value as string);
          },
        });
      });
    }

    if (isFlutterWebView()) {
      if (type === "access_token") {
        return window.flutter_inappwebview!.callHandler("getAccessToken");
      } else {
        return window.flutter_inappwebview!.callHandler("getRefreshToken");
      }
    }

    return Promise.resolve(null);
  },
};

/**
 * Checkout helper functions
 */
export const checkout = {
  /**
   * Check if checkout component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("cashfree") || isFlutterWebView();
  },

  /**
   * Start checkout process
   */
  async start(
    paymentSessionId: string,
    orderId: string
  ): Promise<FlutterCashfreeResponse> {
    if (supportsStradaComponent("cashfree")) {
      return new Promise((resolve, reject) => {
        window.Strada!.web.send({
          component: "cashfree",
          event: "checkout",
          data: {
            orderId,
            paymentSessionId,
            environment: import.meta.env.VITE_CASHFREE_ENV,
            metadata: { url: window.location.href },
          },
          callback: ({ data }) => {
            if (data && "status" in data && data.status === "success") {
              return resolve({ orderId });
            }
            return reject(
              data || new Error("Empty response from native integration")
            );
          },
        });
      });
    }

    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler(
        "initiateCashfree",
        orderId,
        paymentSessionId
      );
    }

    return Promise.reject(new Error("No supported checkout method available"));
  },
};

/**
 * Digio eSign helper functions
 */
export const digioEsign = {
  /**
   * Check if digio-esign component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("digio-esign") || isFlutterWebView();
  },

  /**
   * Submit eSign document
   */
  async submit(
    documentId: string,
    customerIdentifier: string,
    accessToken: string
  ) {
    if (supportsStradaComponent("digio-esign")) {
      return new Promise((resolve, reject) => {
        window.Strada.web.send({
          component: "digio-esign",
          event: "submit",
          data: {
            documentId,
            customerId: customerIdentifier,
            accessToken,
            environment: "production",
            metadata: { url: window.location.href },
          },
          callback: ({ data }) => {
            if (!data) {
              return reject(
                new Error("Empty response from native integration")
              );
            }
            if ("status" in data && typeof data.status === "string") {
              if (data.status === "success") {
                return resolve(data);
              }
            }
            return reject(data);
          },
        });
      });
    }

    if (isFlutterWebView()) {
      const result = await window.flutter_inappwebview!.callHandler(
        "initiateDigio",
        documentId,
        customerIdentifier,
        accessToken
      );
      if (result.errorCode) {
        throw result;
      }
      return result;
    }

    return Promise.reject(new Error("No supported eSign method available"));
  },
};

/**
 * Digio KYC helper functions
 */
export const digioKyc = {
  /**
   * Check if digio-kyc component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("digio-kyc") || isFlutterWebView();
  },

  /**
   * Submit KYC document
   */
  async submit(
    documentId: string,
    customerIdentifier: string,
    accessToken: string
  ) {
    if (supportsStradaComponent("digio-kyc")) {
      return new Promise((resolve, reject) => {
        window.Strada!.web.send({
          component: "digio-kyc",
          event: "submit",
          data: {
            documentId,
            customerId: customerIdentifier,
            accessToken,
            environment: "production",
            metadata: { url: window.location.href },
          },
          callback: ({ data }) => {
            if (!data) {
              return reject(
                new Error("Empty response from native integration")
              );
            }
            if ("status" in data && typeof data.status === "string") {
              if (data.status === "success") {
                return resolve(data);
              }
            }
            return reject(data);
          },
        });
      });
    }

    if (isFlutterWebView()) {
      const result = await window.flutter_inappwebview!.callHandler(
        "initiateDigio",
        documentId,
        customerIdentifier,
        accessToken
      );
      if (result.errorCode) throw result;
      return result;
    }

    return Promise.reject(new Error("No supported KYC method available"));
  },
};

/**
 * HyperKYC helper functions
 */
export const hyperKyc = {
  /**
   * Check if hyperkyc component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("hyperkyc") || isFlutterWebView();
  },

  /**
   * Launch HyperKYC process
   */
  async launch(
    accessToken: string,
    workflowId: string,
    transactionId: string,
    selfieImage: string
  ) {
    if (supportsStradaComponent("hyperkyc")) {
      return new Promise((resolve, reject) => {
        window.Strada!.web.send({
          component: "hyperkyc",
          event: "launch",
          data: {
            accessToken,
            workflowId,
            transactionId,
            inputs: { input_img: selfieImage },
            metadata: { url: window.location.href },
          },
          callback: ({ data }) => {
            if (!data) {
              return reject(
                new Error("Empty response from native integration")
              );
            }
            if ("status" in data && typeof data.status === "string") {
              if (data.status === "auto_approved") {
                return resolve(data);
              }
            }
            return reject(data);
          },
        });
      });
    }

    if (isFlutterWebView()) {
      const result = await window.flutter_inappwebview!.callHandler(
        "initiateHyperVerge",
        workflowId,
        transactionId,
        accessToken,
        selfieImage
      );
      if (result.errorCode || result.name !== "autoApproved") {
        throw result;
      }
      return result;
    }

    return Promise.reject(new Error("No supported HyperKYC method available"));
  },
};

/**
 * Share helper functions
 */
export const share = {
  /**
   * Check if share component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("share") || isFlutterWebView();
  },

  /**
   * Share text content
   */
  shareText(text: string, chooserTitle = "Share") {
    if (supportsStradaComponent("share")) {
      return window.Strada!.web.send({
        component: "share",
        event: "shareText",
        data: {
          text,
          chooserTitle,
          metadata: { url: window.location.href },
        },
      });
    }

    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler("share", text);
    }
  },
};

/**
 * Email helper functions
 */
export const sendEmail = {
  /**
   * Check if emai component is supported
   */
  isSupported(): boolean {
    return supportsStradaComponent("email") || isFlutterWebView();
  },

  /**
   * Email
   */
  shareEmail(to: string, subject: string, body: string, url: string) {
    if (supportsStradaComponent("email")) {
      return window.Strada!.web.send({
        component: "email",
        event: "sendEmail",
        data: {
          to,
          subject,
          body,
          url,
          metadata: { url: window.location.href },
        },
      });
    }

    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler(
        "email",
        to,
        subject,
        body,
        url
      );
    }
  },
};
